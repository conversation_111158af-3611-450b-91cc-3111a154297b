{% comment %} 动态推荐产品滑块 {% endcomment %}
{% if RecommendProducts and RecommendProducts.size > 0 %}
    {% for recommendGroup in RecommendProducts %}
        {% if recommendGroup.IsVisible and recommendGroup.Products.size > 0 %}
            <div class="related-product grid-products" data-recommend-id="{{ recommendGroup.RId }}">
                <div class="section-header">
                    {% if recommendGroup.Title and recommendGroup.Title != "" %}
                        <h2 class="section-header__title text-center h2"><span>{{ recommendGroup.Title }}</span></h2>
                    {% else %}
                        <h2 class="section-header__title text-center h2"><span>Related Products</span></h2>
                    {% endif %}
                    {% if recommendGroup.SubTitle and recommendGroup.SubTitle != "" %}
                        <p class="sub-heading">{{ recommendGroup.SubTitle }}</p>
                    {% endif %}
                </div>
                <div class="productSlider-style2">
                    {% for product in recommendGroup.Products %}
                        <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                            <!-- start product image -->
                            <div class="product-image">
                                <!-- start product image -->
                                <a href="/products/{{ product.PageUrl }}" class="product-img">
                                    <!-- image -->
                                    {% if product.PicPath and product.PicPath != "" %}
                                        <img class="primary blur-up lazyload" data-src="{{ product.PicPath }}" src="{{ product.PicPath }}" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                    {% else %}
                                        <img class="primary blur-up lazyload" data-src="{{static_path}}/assets/images/product-images/product-placeholder.jpg" src="{{static_path}}/assets/images/product-images/product-placeholder.jpg" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                    {% endif %}
                                    <!-- End image -->
                                    <!-- Hover image -->
                                    {% if product.PicPath_1 and product.PicPath_1 != "" %}
                                        <img class="hover blur-up lazyload" data-src="{{ product.PicPath_1 }}" src="{{ product.PicPath_1 }}" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                    {% endif %}
                                    <!-- End hover image -->
                                    <!-- product label -->
                                    {% if product.PromotionPriceFormat and product.PromotionPriceFormat != "" and product.PromotionPriceFormat != "0" %}
                                        <div class="product-labels"><span class="lbl on-sale">Sale</span></div>
                                    {% endif %}
                                    <!-- End product label -->
                                </a>
                                <!-- end product image -->
                                <!--Product Button-->
                                <div class="button-set style3">
                                    <ul>
                                        <li>
                                            <!--Quick View Button-->
                                            <a href="#quickview-popup" title="Quick View" class="btn-icon quick-view-popup quick-view" data-product-id="{{ product.ProductId }}" data-bs-toggle="modal" data-bs-target="#quickview_popup">
                                                <i class="icon an an-expand-arrows-alt"></i>
                                                <span class="tooltip-label">{{ "web.global.quickView"|translate}}</span>
                                            </a>
                                            <!--End Quick View Button-->
                                        </li>
                                        <li>
                                            <!--Wishlist Button-->
                                            <div class="wishlist-btn">
                                                <a class="btn-icon wishlist add-to-wishlist" href="#" data-product-id="{{ product.ProductId }}">
                                                    <i class="icon an an-heart{% if product.IsFavorited %} text-danger{% else %}-o{% endif %}"></i>
                                                    <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                                </a>
                                            </div>
                                            <!--End Wishlist Button-->
                                        </li>
                                    </ul>
                                </div>
                                <!--End Product Button-->
                            </div>
                            <!-- end product image -->
                            <!--start product details -->
                            <div class="product-details text-left">
                                <!-- product name -->
                                <div class="product-name">
                                    <a href="/products/{{ product.PageUrl }}">{{ product.ProductName }}</a>
                                </div>
                                <!-- End product name -->
                                <!-- product price -->
                                <div class="product-price">
                                    {% if product.PromotionPriceFormat and product.PromotionPriceFormat != "" and product.PromotionPriceFormat != "0" %}
                                        <span class="old-price">{{ product.PriceFormat }}</span>
                                        <span class="price">{{ product.PromotionPriceFormat }}</span>
                                    {% else %}
                                        <span class="price">{{ product.PriceFormat }}</span>
                                    {% endif %}
                                </div>
                                <!-- End product price -->
                                <!--Product Review-->
                                {% if product.AverageRating and product.AverageRating > 0 %}
                                    <div class="product-review">
                                        {% assign fullStars = product.AverageRating | floor %}
                                        {% assign hasHalfStar = product.AverageRating | minus: fullStars | times: 10 | floor %}
                                        {% for i in (1..5) %}
                                            {% if i <= fullStars %}
                                                <i class="an an-star"></i>
                                            {% else %}
                                                {% assign nextStar = fullStars | plus: 1 %}
                                                {% if i == nextStar and hasHalfStar >= 5 %}
                                                    <i class="an an-star-half-alt"></i>
                                                {% else %}
                                                    <i class="an an-star-o"></i>
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                        {% if product.ReviewCount and product.ReviewCount > 0 %}
                                            <span class="review-label"><a href="#">{{ product.ReviewCount }} {{ "products.goods.reviews"|translate}}</a></span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                                <!--End Product Review-->
                            </div>
                            <!-- End product details -->
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    {% endfor %}
{% else %}
    <!-- 如果没有推荐产品数据，显示默认内容或隐藏 -->
{% endif %}
